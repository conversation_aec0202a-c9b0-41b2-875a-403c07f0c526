const CV = require('../models/cvModel');
const cloudinary = require('cloudinary').v2;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

const cvController = {
  // Get all CV submissions
  getAllCVs: (req, res) => {
    CV.getAll((err, cvs) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Database error', error: err });
      }
      res.status(200).json({ success: true, message: 'CVs retrieved successfully', data: cvs });
    });
  },

  // Get CV by national ID
  getCVByNationalId: (req, res) => {
    const nationalId = req.params.national_id;
    
    CV.getByNationalId(nationalId, (err, cv) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Database error', error: err });
      }
      
      if (!cv || cv.length === 0) {
        return res.status(404).json({ success: false, message: 'CV not found' });
      }
      
      res.status(200).json({ success: true, message: 'CV retrieved successfully', data: cv[0] });
    });
  },

  // Create or update CV submission
  submitCV: async (req, res) => {
    try {
      const cvData = req.body;
      
      // Validate required fields
      if (!cvData.national_id || !cvData.full_name || !cvData.email || !cvData.phone || !cvData.position) {
        return res.status(400).json({ 
          success: false, 
          message: 'Missing required fields: national_id, full_name, email, phone, position' 
        });
      }
      
      // If CV file was uploaded, process it with Cloudinary
      if (req.files && req.files.cv_file) {
        try {
          // Convert the file buffer to base64 for Cloudinary upload
          const fileBuffer = req.files.cv_file[0].buffer;
          const originalName = req.files.cv_file[0].originalname;
          const base64String = `data:${req.files.cv_file[0].mimetype};base64,${fileBuffer.toString('base64')}`;
          
          // Upload to Cloudinary with specific folder and resource type
          const uploadResult = await cloudinary.uploader.upload(base64String, {
            folder: 'cv_submissions',
            resource_type: 'auto', // Allows non-image files
            public_id: `cv_${cvData.national_id}_${Date.now()}`,
            use_filename: true,
            unique_filename: true,
            access_mode: 'public', // Set access mode to public to prevent authentication prompts
            type: 'upload' // Use 'upload' for public access without authentication
          });
          
          cvData.cv_file_url = uploadResult.secure_url;
          cvData.cv_file_name = originalName;
        } catch (uploadError) {
          console.error('Cloudinary upload error:', uploadError);
          return res.status(500).json({ 
            success: false, 
            message: 'File upload failed', 
            error: uploadError.message 
          });
        }
      }
      
      // Submit/update CV in database
      CV.upsert(cvData, (err, result) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ success: false, message: 'Database error', error: err });
        }
        
        const message = result.isUpdate ? 'CV updated successfully' : 'CV submitted successfully';
        res.status(200).json({ 
          success: true, 
          message: message, 
          data: { national_id: cvData.national_id, isUpdate: result.isUpdate } 
        });
      });
    } catch (error) {
      console.error('CV submission error:', error);
      res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
    }
  },

  // Delete CV
  deleteCV: (req, res) => {
    try {
      const nationalId = req.params.national_id;
      
      // First check if CV exists
      CV.getByNationalId(nationalId, async (err, results) => {
        if (err) {
          console.error("Error checking CV existence:", err);
          return res.status(500).json({ error: "Database error" });
        }
        
        if (results.length === 0) {
          return res.status(404).json({ error: "CV not found" });
        }
        
        const cv = results[0];
        
        // Function to extract public_id from Cloudinary URL
        const extractPublicId = (url) => {
          if (!url || typeof url !== 'string') return null;
          
          // Check if it's a Cloudinary URL
          if (url.includes('res.cloudinary.com')) {
            try {
              // Extract public_id from URL like: https://res.cloudinary.com/cloud_name/image/upload/v123456/folder/file_id.pdf
              const urlParts = url.split('/');
              const uploadIndex = urlParts.indexOf('upload');
              if (uploadIndex !== -1 && uploadIndex < urlParts.length - 1) {
                // Get everything after 'upload' and before file extension
                const pathAfterUpload = urlParts.slice(uploadIndex + 1).join('/');
                // Remove version if present (starts with 'v' followed by numbers)
                const withoutVersion = pathAfterUpload.replace(/^v\d+\//, '');
                // Remove file extension
                const publicId = withoutVersion.replace(/\.[^.]+$/, '');
                return publicId;
              }
            } catch (error) {
              console.error('Error extracting public_id from URL:', url, error);
            }
          }
          return null;
        };
        
        // Collect all Cloudinary public IDs to delete
        const publicIdsToDelete = [];
        
        // Check cv_file_url
        if (cv.cv_file_url) {
          const publicId = extractPublicId(cv.cv_file_url);
          if (publicId) publicIdsToDelete.push(publicId);
        }
        
        // Check if Cloudinary is configured
        const cloudinaryConfigured = process.env.CLOUDINARY_CLOUD_NAME && 
                                    process.env.CLOUDINARY_API_KEY && 
                                    process.env.CLOUDINARY_API_SECRET;
        
        // Delete files from Cloudinary if configured and there are files to delete
        if (cloudinaryConfigured && publicIdsToDelete.length > 0) {
          try {
            // Delete each file from Cloudinary
            const deletePromises = publicIdsToDelete.map(publicId =>
              cloudinary.uploader.destroy(publicId, { resource_type: 'raw' })
                .then(result => {
                  return result;
                })
                .catch(error => {
                  console.error(`Failed to delete file ${publicId} from Cloudinary:`, error);
                  // Don't throw error, continue with CV deletion even if file deletion fails
                  return { error: error.message };
                })
            );
            
            await Promise.all(deletePromises);
          } catch (cloudinaryError) {
            console.error('Error deleting files from Cloudinary:', cloudinaryError);
            // Continue with CV deletion even if Cloudinary deletion fails
          }
        }
        
        // Delete CV from database
        CV.delete(nationalId, (deleteErr, result) => {
          if (deleteErr) {
            console.error("Error deleting CV:", deleteErr);
            return res.status(500).json({ error: "Database error" });
          }
          
          if (result.affectedRows === 0) {
            return res.status(404).json({ error: "CV not found" });
          }
          
          const message = publicIdsToDelete.length > 0 
            ? `CV and ${publicIdsToDelete.length} associated file(s) deleted successfully`
            : "CV deleted successfully";
          
          res.status(200).json({ success: true, message });
        });
      });
    } catch (error) {
      console.error('Error in deleteCV controller:', error);
      res.status(500).json({ error: "Internal server error", details: error.message });
    }
  },

  // Get dashboard analytics
  getDashboardAnalytics: (req, res) => {
    CV.getDashboardAnalytics((err, analytics) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Database error', error: err });
      }
      
      // Format the analytics data
      const formattedAnalytics = {
        totalSubmissions: analytics.totalSubmissions?.[0]?.count || 0,
        recentSubmissions: analytics.recentSubmissions?.[0]?.count || 0,
        genderDistribution: analytics.genderDistribution || []
      };
      
      res.status(200).json({ 
        success: true, 
        message: 'Analytics retrieved successfully', 
        data: formattedAnalytics 
      });
    });
  }
};

module.exports = cvController;