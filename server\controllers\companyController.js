const Company = require('../models/companyModel');
const cloudinary = require('cloudinary').v2;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

const companyController = {
  // Get all companies
  getAllCompanies: (req, res) => {
    Company.getAll((err, companies) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Database error', error: err });
      }
      res.status(200).json({ success: true, message: 'Companies retrieved successfully', data: companies });
    });
  },

  // Get company by ID
  getCompanyById: (req, res) => {
    const companyId = req.params.company_id;
    
    Company.getById(companyId, (err, company) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Database error', error: err });
      }
      
      if (!company || company.length === 0) {
        return res.status(404).json({ success: false, message: 'Company not found' });
      }
      
      res.status(200).json({ success: true, message: 'Company retrieved successfully', data: company[0] });
    });
  },

  // Create new company
  createCompany: async (req, res) => {
    try {
      const companyData = req.body;
      
      // If logo image was uploaded, process it with Cloudinary
      if (req.files && req.files.company_logo) {
        // Convert the file buffer to base64 for Cloudinary upload
        const fileBuffer = req.files.company_logo[0].buffer;
        const base64String = `data:${req.files.company_logo[0].mimetype};base64,${fileBuffer.toString('base64')}`;
        
        // Upload to Cloudinary
        const uploadResult = await cloudinary.uploader.upload(base64String, {
          folder: 'company_logos',
          upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET
        });
        
        // Add the Cloudinary URL to the company data
        companyData.company_logo_url = uploadResult.secure_url;
      }
      
      Company.create(companyData, (err, result) => {
        if (err) {
          return res.status(500).json({ success: false, message: 'Error creating company', error: err });
        }
        
        res.status(201).json({ 
          success: true, 
          message: 'Company created successfully', 
          data: {
            company_id: result.insertId,
            company_name: companyData.company_name,
            company_logo_url: companyData.company_logo_url
          }
        });
      });
    } catch (error) {
      console.error('Error in createCompany:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error });
    }
  },

  // Update company
  updateCompany: async (req, res) => {
    try {
      const companyId = req.params.company_id;
      const companyData = req.body;
      
      // If logo image was uploaded, process it with Cloudinary
      if (req.files && req.files.company_logo) {
        // Convert the file buffer to base64 for Cloudinary upload
        const fileBuffer = req.files.company_logo[0].buffer;
        const base64String = `data:${req.files.company_logo[0].mimetype};base64,${fileBuffer.toString('base64')}`;
        
        // Upload to Cloudinary
        const uploadResult = await cloudinary.uploader.upload(base64String, {
          folder: 'company_logos',
          upload_preset: process.env.CLOUDINARY_UPLOAD_PRESET
        });
        
        // Add the Cloudinary URL to the company data
        companyData.company_logo_url = uploadResult.secure_url;
      }
      
      Company.update(companyId, companyData, (err, result) => {
        if (err) {
          return res.status(500).json({ success: false, message: 'Error updating company', error: err });
        }
        
        if (result.affectedRows === 0) {
          return res.status(404).json({ success: false, message: 'Company not found' });
        }
        
        res.status(200).json({ 
          success: true, 
          message: 'Company updated successfully', 
          data: {
            company_id: companyId,
            company_name: companyData.company_name,
            company_logo_url: companyData.company_logo_url
          }
        });
      });
    } catch (error) {
      console.error('Error in updateCompany:', error);
      res.status(500).json({ success: false, message: 'Server error', error: error });
    }
  },

  // Delete company
  deleteCompany: (req, res) => {
    try {
      const companyId = req.params.company_id;
      
      // First check if company exists
      Company.getById(companyId, async (err, results) => {
        if (err) {
          console.error("Error checking company existence:", err);
          return res.status(500).json({ error: "Database error" });
        }
        
        if (results.length === 0) {
          return res.status(404).json({ error: "Company not found" });
        }
        
        const company = results[0];
        
        // Function to extract public_id from Cloudinary URL
        const extractPublicId = (url) => {
          if (!url || typeof url !== 'string') return null;
          
          // Check if it's a Cloudinary URL
          if (url.includes('res.cloudinary.com')) {
            try {
              // Extract public_id from URL like: https://res.cloudinary.com/cloud_name/image/upload/v123456/folder/image_id.jpg
              const urlParts = url.split('/');
              const uploadIndex = urlParts.indexOf('upload');
              if (uploadIndex !== -1 && uploadIndex < urlParts.length - 1) {
                // Get everything after 'upload' and before file extension
                const pathAfterUpload = urlParts.slice(uploadIndex + 1).join('/');
                // Remove version if present (starts with 'v' followed by numbers)
                const withoutVersion = pathAfterUpload.replace(/^v\d+\//, '');
                // Remove file extension
                const publicId = withoutVersion.replace(/\.[^.]+$/, '');
                return publicId;
              }
            } catch (error) {
              console.error('Error extracting public_id from URL:', url, error);
            }
          }
          return null;
        };
        
        // Collect all Cloudinary public IDs to delete
        const publicIdsToDelete = [];
        
        // Check company_logo
        if (company.company_logo) {
          const publicId = extractPublicId(company.company_logo);
          if (publicId) publicIdsToDelete.push(publicId);
        }
        
        // Check company_banner if exists
        if (company.company_banner) {
          const publicId = extractPublicId(company.company_banner);
          if (publicId) publicIdsToDelete.push(publicId);
        }
        
        // Check if Cloudinary is configured
         const cloudinaryConfigured = process.env.CLOUDINARY_CLOUD_NAME && 
                                     process.env.CLOUDINARY_API_KEY && 
                                     process.env.CLOUDINARY_API_SECRET;
        
        // Delete images from Cloudinary if configured and there are images to delete
        if (cloudinaryConfigured && publicIdsToDelete.length > 0) {
          try {
            // Delete each image from Cloudinary
            const deletePromises = publicIdsToDelete.map(publicId => 
              cloudinary.uploader.destroy(publicId)
                .then(result => {
                  return result;
                })
                .catch(error => {
                  console.error(`Failed to delete image ${publicId} from Cloudinary:`, error);
                  // Don't throw error, continue with company deletion even if image deletion fails
                  return { error: error.message };
                })
            );
            
            await Promise.all(deletePromises);
          } catch (cloudinaryError) {
            console.error('Error deleting images from Cloudinary:', cloudinaryError);
            // Continue with company deletion even if Cloudinary deletion fails
          }
        }
        
        // Delete company from database
        Company.delete(companyId, (deleteErr, result) => {
          if (deleteErr) {
            console.error("Error deleting company:", deleteErr);
            return res.status(500).json({ error: "Database error" });
          }
          
          const message = publicIdsToDelete.length > 0 
            ? `Company and ${publicIdsToDelete.length} associated image(s) deleted successfully`
            : "Company deleted successfully";
          
          res.json({ message });
        });
      });
    } catch (error) {
      console.error('Error in deleteCompany controller:', error);
      res.status(500).json({ error: "Internal server error", details: error.message });
    }
  },

  // Get dashboard analytics for companies
  getDashboardAnalytics: (req, res) => {
    Company.getAll((err, companies) => {
      if (err) {
        return res.status(500).json({ success: false, message: 'Database error', error: err });
      }
      
      const analytics = {
        total_companies: companies.length,
        newest_companies: companies.slice(0, 5) // Get 5 most recent companies
      };
      
      res.status(200).json({ success: true, message: 'Company analytics retrieved successfully', data: analytics });
    });
  }
};

module.exports = companyController;
